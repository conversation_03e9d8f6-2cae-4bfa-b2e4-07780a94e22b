#!/bin/bash
# <PERSON>sh script to completely reset and reseed the Supabase database

echo "🔄 Resetting Supabase Database..."

# Stop all services
echo "⏹️  Stopping Supabase services..."
docker-compose down

# Remove the database volume to completely reset the database
echo "🗑️  Removing database volume..."
if docker volume rm backend_db_data 2>/dev/null; then
    echo "✅ Database volume removed successfully"
else
    echo "ℹ️  Database volume didn't exist or was already removed"
fi

# Remove any orphaned containers
echo "🧹 Cleaning up orphaned containers..."
docker-compose rm -f

# Start services (this will recreate the database with fresh data)
echo "🚀 Starting Supabase services with fresh database..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    attempt=$((attempt + 1))
    sleep 2
    if docker exec supabase-db pg_isready -U postgres -h localhost >/dev/null 2>&1; then
        echo "✅ Database is ready!"
        break
    fi
    echo "⏳ Attempt $attempt/$max_attempts - Database not ready yet..."
done

if [ $attempt -ge $max_attempts ]; then
    echo "❌ Database failed to start after $max_attempts attempts"
    exit 1
fi

# Verify the seed data was loaded
echo "🔍 Verifying seed data..."
post_count=$(docker exec supabase-db psql -U postgres -d postgres -t -c "SELECT COUNT(*) FROM posts;" 2>/dev/null | tr -d ' ')
if [ $? -eq 0 ]; then
    echo "✅ Found $post_count posts in database"
    
    # Show posts with media URLs
    echo "📋 Posts with media:"
    docker exec supabase-db psql -U postgres -d postgres -c "SELECT id, content, media_url FROM posts WHERE media_url IS NOT NULL ORDER BY created_at LIMIT 5;"
else
    echo "❌ Failed to verify seed data"
fi

# Show service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🎉 Database reset complete!"
echo "📱 You can now restart your Flutter app to use the fresh database"
echo "🌐 Supabase Studio: http://localhost:54323"
echo "🔗 API URL: http://localhost:54321"

#!/usr/bin/env python3
"""
Simple HTTP server to serve images for GameFlex development.
This serves the images from the volumes/storage directory.
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

# Configuration
PORT = 8080
STORAGE_DIR = Path(__file__).parent / "volumes" / "storage"

class ImageHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(STORAGE_DIR), **kwargs)
    
    def end_headers(self):
        # Add CORS headers to allow cross-origin requests
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()

def main():
    # Ensure storage directory exists
    STORAGE_DIR.mkdir(parents=True, exist_ok=True)
    
    print(f"🖼️  Starting image server...")
    print(f"📁 Serving files from: {STORAGE_DIR}")
    print(f"🌐 Server URL: http://localhost:{PORT}")
    print(f"📸 Example image URL: http://localhost:{PORT}/cod_screenshot.jpg")
    print(f"🛑 Press Ctrl+C to stop")
    
    try:
        with socketserver.TCPServer(("", PORT), ImageHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

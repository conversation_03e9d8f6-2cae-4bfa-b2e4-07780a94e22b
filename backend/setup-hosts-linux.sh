#!/bin/bash

# GameFlex WSL2 Docker Host Setup Script for Linux/WSL
# This script configures the /etc/hosts file to resolve GameFlex domain names

set -e

REMOVE=false
FORCE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --remove|-r)
            REMOVE=true
            shift
            ;;
        --force|-f)
            FORCE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--remove] [--force]"
            echo "  --remove, -r    Remove GameFlex entries from hosts file"
            echo "  --force, -f     Force overwrite existing entries"
            echo "  --help, -h      Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

HOSTS_FILE="/etc/hosts"
BACKUP_FILE="/etc/hosts.gameflex.backup"

# GameFlex domain entries
GAMEFLEX_ENTRIES=(
    "127.0.0.1 api.gameflex.local"
    "127.0.0.1 studio.gameflex.local"
    "127.0.0.1 auth.gameflex.local"
    "127.0.0.1 rest.gameflex.local"
    "127.0.0.1 storage.gameflex.local"
    "127.0.0.1 imgproxy.gameflex.local"
    "127.0.0.1 meta.gameflex.local"
    "127.0.0.1 realtime.gameflex.local"
    "127.0.0.1 functions.gameflex.local"
    "127.0.0.1 db.gameflex.local"
    "127.0.0.1 postgres.gameflex.local"
    "127.0.0.1 kong.gameflex.local"
)

START_MARKER="# GameFlex Development - START"
END_MARKER="# GameFlex Development - END"

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]]; then
    echo "❌ This script must be run as root or with sudo!"
    echo "   Try: sudo $0"
    exit 1
fi

remove_gameflex_entries() {
    echo "🗑️  Removing GameFlex entries from hosts file..."
    
    if [[ ! -f "$HOSTS_FILE" ]]; then
        echo "❌ Hosts file not found: $HOSTS_FILE"
        return 1
    fi
    
    # Create temporary file without GameFlex entries
    awk "
        BEGIN { in_section = 0 }
        /^$START_MARKER/ { in_section = 1; next }
        /^$END_MARKER/ { in_section = 0; next }
        !in_section { print }
    " "$HOSTS_FILE" > "$HOSTS_FILE.tmp"
    
    mv "$HOSTS_FILE.tmp" "$HOSTS_FILE"
    echo "✅ GameFlex entries removed from hosts file"
}

add_gameflex_entries() {
    echo "📝 Adding GameFlex entries to hosts file..."
    
    # Create backup if it doesn't exist
    if [[ ! -f "$BACKUP_FILE" ]]; then
        echo "💾 Creating backup of hosts file..."
        cp "$HOSTS_FILE" "$BACKUP_FILE"
    fi
    
    # Check if entries already exist
    if grep -q "$START_MARKER" "$HOSTS_FILE"; then
        if [[ "$FORCE" != "true" ]]; then
            echo "⚠️  GameFlex entries already exist in hosts file!"
            echo "   Use --force to overwrite existing entries"
            return 0
        else
            echo "🔄 Removing existing entries..."
            remove_gameflex_entries
        fi
    fi
    
    # Add new entries
    {
        echo ""
        echo "$START_MARKER"
        echo "# These entries allow GameFlex Docker containers to be accessed via domain names"
        echo "# Generated by setup-hosts-linux.sh"
        for entry in "${GAMEFLEX_ENTRIES[@]}"; do
            echo "$entry"
        done
        echo "$END_MARKER"
    } >> "$HOSTS_FILE"
    
    echo "✅ GameFlex entries added to hosts file"
}

test_gameflex_domains() {
    echo "🔍 Testing GameFlex domain resolution..."
    
    local test_domains=("api.gameflex.local" "studio.gameflex.local" "db.gameflex.local")
    local all_good=true
    
    for domain in "${test_domains[@]}"; do
        if command -v nslookup >/dev/null 2>&1; then
            result=$(nslookup "$domain" 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}')
        elif command -v dig >/dev/null 2>&1; then
            result=$(dig +short "$domain" 2>/dev/null)
        else
            # Fallback to getent
            result=$(getent hosts "$domain" 2>/dev/null | awk '{print $1}')
        fi
        
        if [[ "$result" == "127.0.0.1" ]]; then
            echo "   ✅ $domain → 127.0.0.1"
        else
            echo "   ❌ $domain → $result (expected 127.0.0.1)"
            all_good=false
        fi
    done
    
    if [[ "$all_good" == "true" ]]; then
        echo "🎉 All GameFlex domains are resolving correctly!"
    else
        echo "⚠️  Some domains are not resolving correctly. Check your hosts file."
    fi
}

# Main execution
echo ""
echo "🎮 GameFlex WSL2 Docker Host Setup"
echo ""

if [[ "$REMOVE" == "true" ]]; then
    remove_gameflex_entries
    echo ""
    echo "🔧 To restore original hosts file:"
    echo "   sudo cp '$BACKUP_FILE' '$HOSTS_FILE'"
else
    add_gameflex_entries
    echo ""
    test_gameflex_domains
    echo ""
    echo "🎯 GameFlex Services will be available at:"
    echo "   📊 Supabase Studio: http://studio.gameflex.local:54323"
    echo "   🔌 API Gateway: http://api.gameflex.local:54321"
    echo "   🗄️  Database: db.gameflex.local:54322"
    echo ""
    echo "🔧 To remove these entries later:"
    echo "   sudo $0 --remove"
fi

echo ""

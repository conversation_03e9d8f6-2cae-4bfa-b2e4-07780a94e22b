-- Realtime setup for Supabase
-- This script sets up the realtime functionality

-- Create realtime schemas
CREATE SCHEMA IF NOT EXISTS realtime;
CREATE SCHEMA IF NOT EXISTS _realtime;

-- Create realtime extension
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Create realtime publication
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime'
  ) THEN
    CREATE PUBLICATION supabase_realtime;
  END IF;
END $$;

-- Create realtime tables
CREATE TABLE IF NOT EXISTS _realtime.subscription (
    id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] NOT NULL DEFAULT '{}',
    claims jsonb NOT NULL,
    claims_role regrole NOT NULL GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED,
    created_at timestamp without time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

CREATE TABLE IF NOT EXISTS _realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);

-- Create tenants table for Realtime
CREATE TABLE IF NOT EXISTS _realtime.tenants (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    external_id text NOT NULL UNIQUE,
    jwt_secret text NOT NULL,
    postgres_cdc_default text DEFAULT 'postgres_cdc_rls',
    max_concurrent_users integer DEFAULT 200,
    max_events_per_second integer DEFAULT 100,
    max_bytes_per_second integer DEFAULT 100000,
    max_channels_per_client integer DEFAULT 100,
    max_joins_per_second integer DEFAULT 100,
    suspend boolean DEFAULT false,
    inserted_at timestamp without time zone NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at timestamp without time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Create extensions table for Realtime
CREATE TABLE IF NOT EXISTS _realtime.extensions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    type text NOT NULL,
    settings jsonb DEFAULT '{}',
    tenant_external_id text NOT NULL REFERENCES _realtime.tenants(external_id) ON DELETE CASCADE,
    inserted_at timestamp without time zone NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at timestamp without time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Insert default tenant
INSERT INTO _realtime.tenants (name, external_id, jwt_secret)
VALUES ('realtime', 'realtime', 'your-super-secret-jwt-token-with-at-least-32-characters-long')
ON CONFLICT (external_id) DO NOTHING;

-- Insert default extension
INSERT INTO _realtime.extensions (type, tenant_external_id, settings)
VALUES ('postgres_cdc_rls', 'realtime', '{"db_host": "db", "db_name": "postgres", "db_user": "supabase_admin", "db_password": "your-super-secret-and-long-postgres-password", "db_port": "5432", "region": "us-east-1", "poll_interval": 100, "poll_max_record_bytes": 1048576}')
ON CONFLICT DO NOTHING;

-- Create realtime types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_defined_filter') THEN
        CREATE TYPE realtime.user_defined_filter AS (
            column_name text,
            op realtime.equality_op,
            value text
        );
    END IF;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'equality_op') THEN
        CREATE TYPE realtime.equality_op AS ENUM ('eq', 'neq', 'lt', 'lte', 'gt', 'gte', 'in');
    END IF;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create realtime functions
CREATE OR REPLACE FUNCTION realtime.to_regrole(role_name text)
RETURNS regrole
LANGUAGE sql
AS $function$
    SELECT role_name::regrole
$function$;

CREATE OR REPLACE FUNCTION realtime.can_insert_object(fid bigint, table_name text, role_name text, defined_columns text[])
RETURNS boolean
LANGUAGE plpgsql
AS $function$
DECLARE
    upsert_privilege boolean;
BEGIN
    SELECT privilege_type = 'INSERT'
    FROM information_schema.table_privileges
    WHERE grantee = role_name
      AND table_name = table_name
    INTO upsert_privilege;

    RETURN upsert_privilege;
END
$function$;

CREATE OR REPLACE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer DEFAULT 1024, max_record_bytes integer DEFAULT 1048576)
RETURNS SETOF realtime.wal_rls
LANGUAGE sql
SET log_min_messages TO 'fatal'
AS $function$
    SELECT
        *
    FROM
        pg_logical_slot_get_changes(
            slot_name, NULL, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '1',
            'actions', 'insert,update,delete',
            'add-tables', publication
        )
$function$;

-- Create WAL RLS type
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wal_rls') THEN
        CREATE TYPE realtime.wal_rls AS (
            wal jsonb,
            is_rls_enabled boolean,
            subscription_ids uuid[],
            errors text[]
        );
    END IF;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add tables to realtime publication (only if they exist)
DO $$
BEGIN
    -- Check if public schema tables exist before adding to publication
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'posts') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE public.posts;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'comments') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE public.comments;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'likes') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE public.likes;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notifications') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;
    END IF;

    -- Check if gameflex schema and tables exist before adding to publication
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'gameflex' AND table_name = 'posts') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.posts;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'gameflex' AND table_name = 'comments') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.comments;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'gameflex' AND table_name = 'likes') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.likes;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'gameflex' AND table_name = 'notifications') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.notifications;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'gameflex' AND table_name = 'channel_members') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE gameflex.channel_members;
    END IF;
EXCEPTION
    WHEN others THEN
        -- Log the error but continue
        RAISE NOTICE 'Error adding tables to realtime publication: %', SQLERRM;
END $$;

-- Grant permissions
GRANT USAGE ON SCHEMA _realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA _realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA _realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA _realtime TO anon, authenticated, service_role;

-- Grant realtime schema permissions
GRANT USAGE ON SCHEMA realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA realtime TO anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA realtime TO anon, authenticated, service_role;

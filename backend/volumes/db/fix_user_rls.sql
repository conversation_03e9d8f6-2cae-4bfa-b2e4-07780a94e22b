-- Fix RLS policies for user creation
-- This script adds the missing INSERT policy for the users table

-- Add RLS policy to allow users to insert their own user record
-- This is needed when a user signs up and we need to create their profile
CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Also add a policy for service role to insert users (for system operations)
CREATE POLICY "Service role can insert users" ON public.users
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Add similar policies for user_profiles table
CREATE POLICY "User profiles are viewable by everyone" ON public.user_profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own user profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own user profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Add policies for comments
CREATE POLICY "Comments are viewable by everyone" ON public.comments
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own comments" ON public.comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id);

-- Add policies for channels
CREATE POLICY "Channels are viewable by everyone" ON public.channels
    FOR SELECT USING (true);

CREATE POLICY "Users can insert channels" ON public.channels
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Channel owners can update their channels" ON public.channels
    FOR UPDATE USING (auth.uid() = owner_id);

-- Add policies for channel members
CREATE POLICY "Channel members are viewable by everyone" ON public.channel_members
    FOR SELECT USING (true);

CREATE POLICY "Users can join channels" ON public.channel_members
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave channels" ON public.channel_members
    FOR DELETE USING (auth.uid() = user_id);

-- Add policies for follows
CREATE POLICY "Follows are viewable by everyone" ON public.follows
    FOR SELECT USING (true);

CREATE POLICY "Users can follow others" ON public.follows
    FOR INSERT WITH CHECK (auth.uid() = follower_id);

CREATE POLICY "Users can unfollow others" ON public.follows
    FOR DELETE USING (auth.uid() = follower_id);

-- Add policies for notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Grant additional permissions to ensure everything works
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

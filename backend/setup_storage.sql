-- Create storage bucket for media files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'media',
    'media',
    true,
    52428800, -- 50MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/webm']
) ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for public access
INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command)
VALUES (
    'media_public_read',
    'media',
    'Public read access for media files',
    'true',
    'true',
    'SELECT'
) ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command)
VALUES (
    'media_authenticated_upload',
    'media',
    'Authenticated users can upload media files',
    'auth.role() = ''authenticated''',
    'auth.role() = ''authenticated''',
    'INSERT'
) ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command)
VALUES (
    'media_authenticated_update',
    'media',
    'Authenticated users can update their own media files',
    'auth.uid()::text = (storage.foldername(name))[1]',
    'auth.uid()::text = (storage.foldername(name))[1]',
    'UPDATE'
) ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command)
VALUES (
    'media_authenticated_delete',
    'media',
    'Authenticated users can delete their own media files',
    'auth.uid()::text = (storage.foldername(name))[1]',
    'auth.uid()::text = (storage.foldername(name))[1]',
    'DELETE'
) ON CONFLICT (id) DO NOTHING;

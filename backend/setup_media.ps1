#!/usr/bin/env pwsh

Write-Host "🎮 Setting up GameFlex Media Storage..." -ForegroundColor Green

# Check if Docker is running
try {
    docker ps | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker is not running. Please start Docker first." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Docker is not available. Please install Docker first." -ForegroundColor Red
    exit 1
}

# Check if containers are running
$containers = @("supabase-db", "supabase-storage")
foreach ($container in $containers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if (-not $status) {
        Write-Host "❌ Container $container is not running. Please start the backend first with .\start.ps1" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ Docker containers are running" -ForegroundColor Green

# Set up storage bucket
Write-Host "📦 Setting up storage bucket..." -ForegroundColor Yellow
try {
    docker exec supabase-db psql -U postgres -d postgres -f /docker-entrypoint-initdb.d/../setup_storage.sql
    Write-Host "✅ Storage bucket created" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Storage bucket may already exist" -ForegroundColor Yellow
}

# Create media directory in storage volume
Write-Host "📁 Creating media directory..." -ForegroundColor Yellow
docker exec supabase-storage mkdir -p /var/lib/storage/media

# Copy images to storage
Write-Host "🖼️  Copying images to storage..." -ForegroundColor Yellow

$images = @(
    "cod_screenshot.jpg",
    "diablo_screenshot.jpg", 
    "minecraft_screenshot.webp",
    "wow_screenshot.png"
)

foreach ($image in $images) {
    $sourcePath = "volumes/storage/$image"
    if (Test-Path $sourcePath) {
        Write-Host "   Copying $image..." -ForegroundColor Cyan
        docker cp $sourcePath supabase-storage:/var/lib/storage/media/$image
        Write-Host "   ✅ $image copied" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  $image not found at $sourcePath" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Media storage setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📸 Test image URLs:" -ForegroundColor Cyan
foreach ($image in $images) {
    Write-Host "   http://localhost:54321/storage/v1/object/public/media/$image" -ForegroundColor White
}
Write-Host ""
Write-Host "💡 You can now test the TikTok-style feed in the Flutter app!" -ForegroundColor Yellow

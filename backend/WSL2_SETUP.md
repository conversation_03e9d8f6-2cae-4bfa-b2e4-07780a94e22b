# GameFlex WSL2 Docker Setup Guide

This guide explains how to set up and use the GameFlex backend with WSL2 and Docker Desktop, including custom domain names for better development experience.

## Prerequisites

- Windows 10/11 with WSL2 enabled
- Docker Desktop for Windows with WSL2 backend
- PowerShell (for Windows scripts) or Bash (for Linux/WSL scripts)

## Quick Start

### 1. Start the Backend

**Windows (PowerShell):**
```powershell
cd backend
.\start.ps1
```

**Linux/WSL (Bash):**
```bash
cd backend
./start.sh
```

### 2. Set Up Domain Names (Optional but Recommended)

For better development experience, you can configure custom domain names like `api.gameflex.local` instead of `localhost`.

**Windows (Run as Administrator):**
```powershell
.\setup-hosts-windows.ps1
```

**Linux/WSL:**
```bash
sudo ./setup-hosts-linux.sh
```

### 3. Test Everything Works

**Windows:**
```powershell
.\test-wsl2-setup.ps1
```

**Linux/WSL:**
```bash
./test-wsl2-setup.sh
```

## Domain Names vs Localhost

### With Domain Names (Recommended)
- 📊 Supabase Studio: http://studio.gameflex.local:54323
- 🔌 API Gateway: http://api.gameflex.local:54321
- 🗄️ Database: db.gameflex.local:54322

### With Localhost (Fallback)
- 📊 Supabase Studio: http://localhost:54323
- 🔌 API Gateway: http://localhost:54321
- 🗄️ Database: localhost:54322

## Why Use Domain Names?

1. **Better WSL2 Compatibility**: Domain names work more reliably across WSL2 and Windows
2. **Consistent URLs**: Same URLs work from both Windows and WSL2 environments
3. **Professional Development**: Mimics production-like domain structure
4. **Network Isolation**: Easier to identify GameFlex traffic in network tools

## Network Architecture

The setup creates a custom Docker network with the following services:

```
gameflex_network (**********/16)
├── studio.gameflex.local     → Supabase Studio
├── api.gameflex.local        → Kong API Gateway
├── auth.gameflex.local       → Authentication Service
├── rest.gameflex.local       → REST API
├── storage.gameflex.local    → Storage Service
├── imgproxy.gameflex.local   → Image Proxy
├── meta.gameflex.local       → Meta Service
├── realtime.gameflex.local   → Realtime Service
├── functions.gameflex.local  → Edge Functions
├── db.gameflex.local         → PostgreSQL Database
└── postgres.gameflex.local   → PostgreSQL Database (alias)
```

## Configuration Files

### Environment Variables (.env)
Key changes for WSL2 compatibility:
- `API_EXTERNAL_URL=http://api.gameflex.local:54321`
- `SUPABASE_PUBLIC_URL=http://api.gameflex.local:54321`
- `SITE_URL=http://studio.gameflex.local:54323`

### Docker Compose (docker-compose.yml)
- Custom network with subnet `**********/16`
- Domain aliases for all services
- Proper service dependencies and health checks

## Troubleshooting

### Domain Names Not Working

1. **Check hosts file configuration:**
   ```powershell
   # Windows
   Get-Content C:\Windows\System32\drivers\etc\hosts | Select-String "gameflex"
   ```
   ```bash
   # Linux/WSL
   grep gameflex /etc/hosts
   ```

2. **Re-run host setup:**
   ```powershell
   # Windows (as Administrator)
   .\setup-hosts-windows.ps1 -Force
   ```
   ```bash
   # Linux/WSL
   sudo ./setup-hosts-linux.sh --force
   ```

3. **Test domain resolution:**
   ```powershell
   # Windows
   nslookup api.gameflex.local
   ```
   ```bash
   # Linux/WSL
   nslookup api.gameflex.local
   ```

### Docker Issues

1. **Check Docker is running:**
   ```bash
   docker version
   ```

2. **Check container status:**
   ```bash
   docker-compose ps
   ```

3. **View container logs:**
   ```bash
   docker-compose logs -f [service-name]
   ```

4. **Restart services:**
   ```bash
   docker-compose restart
   ```

### WSL2 Specific Issues

1. **Port forwarding issues:**
   - Restart Docker Desktop
   - Check Windows Firewall settings
   - Verify WSL2 integration is enabled in Docker Desktop

2. **Network connectivity:**
   - Run the test script: `.\test-wsl2-setup.ps1`
   - Check if services are accessible from both Windows and WSL2

## Scripts Reference

### Management Scripts
- `start.ps1` / `start.sh` - Start all services
- `stop.ps1` / `stop.sh` - Stop all services
- `reset-database.ps1` / `reset-database.sh` - Reset database with fresh data

### Setup Scripts
- `setup-hosts-windows.ps1` - Configure Windows hosts file
- `setup-hosts-linux.sh` - Configure Linux/WSL hosts file

### Testing Scripts
- `test-wsl2-setup.ps1` - Comprehensive test suite (Windows)
- `test-wsl2-setup.sh` - Comprehensive test suite (Linux/WSL)

### Script Options

**Host Setup Scripts:**
```powershell
# Windows
.\setup-hosts-windows.ps1 -Remove    # Remove domain entries
.\setup-hosts-windows.ps1 -Force     # Overwrite existing entries
```

```bash
# Linux/WSL
sudo ./setup-hosts-linux.sh --remove  # Remove domain entries
sudo ./setup-hosts-linux.sh --force   # Overwrite existing entries
```

**Test Scripts:**
```powershell
# Windows
.\test-wsl2-setup.ps1 -Verbose       # Show detailed error messages
.\test-wsl2-setup.ps1 -SkipDomains   # Test with localhost only
```

```bash
# Linux/WSL
./test-wsl2-setup.sh --verbose       # Show detailed error messages
./test-wsl2-setup.sh --skip-domains  # Test with localhost only
```

## Security Notes

⚠️ **Development Only**: This setup is designed for development environments only.

- Default passwords are insecure
- JWT secrets are public
- All services are accessible without authentication
- Domain names resolve to localhost (127.0.0.1)

## Support

If you encounter issues:

1. Run the test script to identify problems
2. Check the troubleshooting section above
3. Review Docker and container logs
4. Ensure WSL2 and Docker Desktop are properly configured

For WSL2-specific Docker issues, refer to the [Docker Desktop WSL2 documentation](https://docs.docker.com/desktop/wsl/).

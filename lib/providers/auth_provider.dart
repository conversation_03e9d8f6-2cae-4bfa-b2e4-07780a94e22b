import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/user_service.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class Auth<PERSON>rovider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  StreamSubscription<AuthState>? _authSubscription;

  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    _setStatus(AuthStatus.loading);

    try {
      // Try to restore session from stored tokens
      final sessionRestored = await AuthService.instance.restoreSession();

      if (sessionRestored && AuthService.instance.isAuthenticated) {
        final supabaseUser = AuthService.instance.currentUser;
        if (supabaseUser != null) {
          _user = UserModel.fromSupabaseUser(supabaseUser);
          _setStatus(AuthStatus.authenticated);
          // Ensure user record exists in public.users table (background operation)
          UserService.instance.ensureUserExists(supabaseUser);
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }

      // Listen to auth state changes
      _authSubscription = AuthService.instance.authStateChanges.listen(
        _onAuthStateChange,
        onError: (error) {
          _setError('Authentication error: $error');
        },
      );
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    }
  }

  /// Handle auth state changes
  void _onAuthStateChange(AuthState authState) {
    switch (authState.event) {
      case AuthChangeEvent.initialSession:
        if (authState.session?.user != null) {
          _user = UserModel.fromSupabaseUser(authState.session!.user);
          _setStatus(AuthStatus.authenticated);
          // Ensure user record exists in public.users table (background operation)
          UserService.instance.ensureUserExists(authState.session!.user);
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
        break;
      case AuthChangeEvent.signedIn:
        if (authState.session?.user != null) {
          _user = UserModel.fromSupabaseUser(authState.session!.user);
          _setStatus(AuthStatus.authenticated);
          // Ensure user record exists in public.users table (background operation)
          UserService.instance.ensureUserExists(authState.session!.user);
        }
        break;
      case AuthChangeEvent.signedOut:
        _user = null;
        _setStatus(AuthStatus.unauthenticated);
        break;
      case AuthChangeEvent.userUpdated:
        if (authState.session?.user != null) {
          _user = UserModel.fromSupabaseUser(authState.session!.user);
          notifyListeners();
        }
        break;
      case AuthChangeEvent.passwordRecovery:
        // Handle password recovery if needed
        break;
      case AuthChangeEvent.tokenRefreshed:
        // Token refreshed, no action needed
        break;
      case AuthChangeEvent.mfaChallengeVerified:
        // Handle MFA if needed
        break;
      default:
        // Handle any other auth events
        break;
    }
  }

  /// Sign up with email and password
  Future<bool> signUp({required String email, required String password}) async {
    _setStatus(AuthStatus.loading);

    try {
      final response = await AuthService.instance.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Ensure user record exists in public.users table
        await UserService.instance.ensureUserExists(response.user!);

        _user = UserModel.fromSupabaseUser(response.user!);
        _setStatus(AuthStatus.authenticated);
        return true;
      } else {
        _setError('Sign up failed: No user returned');
        return false;
      }
    } on AuthException catch (e) {
      _setError('Sign up failed: ${e.message}');
      return false;
    } catch (e) {
      _setError('Sign up failed: $e');
      return false;
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({required String email, required String password}) async {
    _setStatus(AuthStatus.loading);

    try {
      final response = await AuthService.instance.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Ensure user record exists in public.users table
        await UserService.instance.ensureUserExists(response.user!);

        _user = UserModel.fromSupabaseUser(response.user!);
        _setStatus(AuthStatus.authenticated);
        return true;
      } else {
        _setError('Sign in failed: No user returned');
        return false;
      }
    } on AuthException catch (e) {
      _setError('Sign in failed: ${e.message}');
      return false;
    } catch (e) {
      _setError('Sign in failed: $e');
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setStatus(AuthStatus.loading);

    try {
      await AuthService.instance.signOut();
      _user = null;
      _setStatus(AuthStatus.unauthenticated);
    } catch (e) {
      _setError('Sign out failed: $e');
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await AuthService.instance.resetPassword(email);
      return true;
    } on AuthException catch (e) {
      _setError('Password reset failed: ${e.message}');
      return false;
    } catch (e) {
      _setError('Password reset failed: $e');
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(AuthStatus status) {
    _status = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
